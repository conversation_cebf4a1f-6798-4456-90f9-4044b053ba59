import {
    JsonObject,
    NodeApiError,
    type IDataObject,
    type IExecuteFunctions,
    type IHookFunctions,
    type IHttpRequestMethods,
    type ILoadOptionsFunctions,
    type IRequestOptions
} from 'n8n-workflow';


let systemFieldsCache: any = null;
let cacheTimestamp: number = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds

// Helper function to get cached system fields
export async function getCachedSystemFields(this: IHookFunctions | IExecuteFunctions | ILoadOptionsFunctions): Promise<any[]> {
    const now = Date.now();

    // Check if cache is valid (not expired)
    if (systemFieldsCache && (now - cacheTimestamp) < CACHE_DURATION) {
        console.log('Using cached system fields');
        return systemFieldsCache;
    }

    // Cache is expired or doesn't exist, fetch fresh data
    console.log('Fetching fresh system fields from API');
    const customFields = await kylasApiRequest.call(this, 'GET', '/v1/layouts/leads/system-fields?view=create', {});
    const fields = JSON.parse(customFields.data);

    // Update cache
    systemFieldsCache = fields;
    cacheTimestamp = now;

    return fields;
}

export async function kylasApiRequest(
    this: IHookFunctions | IExecuteFunctions | ILoadOptionsFunctions,
    method: IHttpRequestMethods,
    endpoint: string,
    body: IDataObject
): Promise<any> {
    // const authenticationMethod = this.getNodeParameter('authentication', 0);
    console.log("URI ->" + `https://api-qa.sling-dev.com${endpoint}`);
    const options: IRequestOptions = {
        headers: {
            Accept: 'application/json',
        },
        method,
        uri: `https://api-qa.sling-dev.com${endpoint}`,
    };

    // console.log("options->" + JSON.stringify(options));

    if (Object.keys(body).length !== 0) {
        options.body = body;
        options.json = true;
    }


    try {
        const credentialType = 'kylasApi';
        // console.log("option->" + JSON.stringify(options.body));
        const responseData = await this.helpers.requestWithAuthentication.call(
            this,
            credentialType,
            options,
        );


        // console.log("responseData 2->" + JSON.stringify(responseData));
        if (responseData.success === false) {
            throw new NodeApiError(this.getNode(), responseData as JsonObject);
        }
        console.log("Return success")
        return {
            data: responseData
        };
    } catch (error) {
        throw new NodeApiError(this.getNode(), error as JsonObject);
    }
}
