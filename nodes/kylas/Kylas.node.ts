import {
    IExecuteFunctions,
    INodeType,
    INodeTypeDescription,
    INodeExecutionData,
    IDataObject,
    IHttpRequestMethods,
    INodePropertyOptions,
    ILoadOptionsFunctions
} from 'n8n-workflow';
import { kylasApiRequest, getCachedSystemFields } from './GenericFunctions';

// Cache for system fields to avoid repeated API calls


export class <PERSON>yla<PERSON> implements INodeType {
    description: INodeTypeDescription = {
        displayName: 'Kyla<PERSON>',
        name: 'kyla<PERSON>',
        icon: 'file:kylas2.svg', // Assumes a file named nasa.svg in the same directory
        group: ['transform'], // This makes it an 'input' node, good for fetching data
        version: 1,
        subtitle: '={{$parameter["operation"] + ": " + $parameter["resource"]}}',
        description: 'Access Kylas Data',
        defaults: {
            name: 'Kyla<PERSON>',
        },
        usableAsTool: true,
        inputs: ['main'],
        outputs: ['main'],
        credentials: [
            {
                name: 'kylas<PERSON><PERSON>',
                required: true,
                displayOptions: {
                    show: {
                        authentication: ['apiToken'],
                    },
                }
            }
        ],
        properties: [
            {
                displayName: 'Authentication',
                name: 'authentication',
                type: 'options',
                options: [
                    {
                        name: 'API Token',
                        value: 'apiToken',
                    },
                ],
                default: 'apiToken',
            },
            {
                displayName: 'Resource',
                name: 'resource',
                noDataExpression: true,
                type: 'options',
                options: [
                    {
                        name: 'Lead',
                        value: 'lead',
                    },
                ],
                default: 'lead',
            },
            // --- Operations ---
            {

                displayName: 'Operation',
                name: 'operation',
                noDataExpression: true,
                displayOptions: {
                    show: {
                        resource: ['lead'],
                    }
                },
                type: 'options',
                options: [
                    {
                        name: 'Create',
                        value: 'create',
                        description: 'Create a lead',
                        action: 'Create a lead'
                    },
                ],
                default: 'create'
            },
            {
                displayName: "First Name",
                name: "firstName",
                description: 'First name of the lead',
                type: 'string',
                required: true,
                default: '',
                displayOptions: {
                    show: {
                        resource: ['lead'],
                        operation: ['create'],
                    }
                }
            },
            {
                displayName: "Last Name",
                name: "lastName",
                description: 'Last name of the lead',
                type: 'string',
                required: true,
                default: '',
                displayOptions: {
                    show: {
                        resource: ['lead'],
                        operation: ['create'],
                    }
                }
            },
            {
                displayName: "Additional Field",
                name: "additionalFields",
                type: "collection",
                placeholder: "Add Field",
                default: {},
                displayOptions: {
                    show: {
                        resource: ['lead'],
                        operation: ['create'],
                    }
                },
                options: [
                    {
                        displayName: "Address",
                        name: "address",
                        type: "string",
                        default: "",
                    },
                    {
                        displayName: "City",
                        name: "city",
                        type: "string",
                        default: "",
                    },
                    {
                        displayName: "Company Address",
                        name: "companyAddress",
                        type: "string",
                        default: "",
                    },
                    {
                        displayName: "Company Annual Revenue",
                        name: "companyAnnualRevenue",
                        type: "number",
                        default: "",
                    },
                    {
                        displayName: "Company City",
                        name: "companyCity",
                        type: "string",
                        default: "",
                    },
                    {
                        displayName: "Company Name",
                        name: "companyName",
                        type: "string",
                        default: "",
                    },
                    {
                        displayName: "Company Phones",
                        name: "companyPhones",
                        type: "fixedCollection",
                        default: {
                            displayName: "Company Phone",
                            name: "value",
                            type: "string",
                            default: "",
                            description: "Phone number without country code",
                        },
                        description: "Add phone numbers",
                        typeOptions: {
                            multipleValues: true,
                        },
                        options: [
                            {
                                displayName: "Company Number",
                                name: "companyPhone",
                                values: [
                                    {
                                        displayName: "Company Phone",
                                        name: "value",
                                        type: "string",
                                        default: "",
                                        description: "Phone number without country code",
                                    },
                                    {
                                        displayName: "Country Code",
                                        name: "code",
                                        type: "string",
                                        default: "IN",
                                        description: "Country code (e.g., IN, US)",
                                    },
                                    {
                                        displayName: "Dial Code",
                                        name: "dialCode",
                                        type: "string",
                                        default: "+91",
                                        description: "Dial code with + prefix (e.g., +91, +1)",
                                    },
                                    {
                                        displayName: "Primary",
                                        name: "primary",
                                        type: "boolean",
                                        default: false,
                                        description: "Whether this is the primary phone number",
                                    },
                                    {
                                        displayName: "Type",
                                        name: "type",
                                        type: "options",
                                        options: [
                                            { name: "Mobile", value: "MOBILE" },
                                            { name: "Home", value: "HOME" },
                                            { name: "Work", value: "WORK" },
                                        ],
                                        default: "MOBILE",
                                        description: "Type of phone number",
                                    },
                                ],
                            },
                        ],
                    },
                    {
                        displayName: "Company State",
                        name: "companyState",
                        type: "string",
                        default: "",
                    },
                    {
                        displayName: "Company Website",
                        name: "companyWebsite",
                        type: "string",
                        default: "",
                    },
                    {
                        displayName: "Company Zipcode",
                        name: "companyZipcode",
                        type: "string",
                        default: "",
                    },
                    {
                        displayName: "Department",
                        name: "department",
                        type: "string",
                        default: "",
                    },
                    {
                        displayName: "Designation",
                        name: "designation",
                        type: "string",
                        default: "",
                    },
                    {
                        displayName: "Do Not Disturb",
                        name: "dnd",
                        type: "boolean",
                        default: false,
                    },
                    {
                        displayName: "Emails",
                        name: "emails",
                        type: "fixedCollection",
                        default: {
                            displayName: "Email ID",
                            name: "value",
                            type: "string",
                            default: "",
                        },
                        description: "Add emails",
                        typeOptions: {
                            multipleValues: true,
                        },
                        options: [
                            {
                                displayName: "Email",
                                name: "email",
                                values: [
                                    { displayName: "Email ID", name: "value", type: "string", default: "" },
                                    { displayName: "Primary", name: "primary", type: "boolean", default: false },
                                    {
                                        displayName: "Type",
                                        name: "type",
                                        type: "options",
                                        options: [
                                            { name: "Office", value: "OFFICE" },
                                            { name: "Personal", value: "PERSONAL" },
                                        ],
                                        default: "OFFICE",
                                        description: "Type of email",
                                    },
                                ],
                            },
                        ],
                    },
                    {
                        displayName: "Expected Closure On",
                        name: "expectedClosureOn",
                        type: "dateTime",
                        default: "",
                        description: "Expected closure date and time (UTC)",
                    },
                    {
                        displayName: "Facebook",
                        name: "facebook",
                        type: "string",
                        default: "",
                    },
                    {
                        displayName: "Linked In",
                        name: "linkedIn",
                        type: "string",
                        default: "",
                    },
                    {
                        displayName: "Phone Numbers",
                        name: "phoneNumbers",
                        type: "fixedCollection",
                        default: {
                            displayName: "Phone Number",
                            name: "value",
                            type: "string",
                            default: "",
                            description: "Phone number without country code",
                        },
                        description: "Add phone numbers",
                        typeOptions: {
                            multipleValues: true,
                        },
                        options: [
                            {
                                displayName: "Phone Number",
                                name: "phoneNumber",
                                values: [
                                    {
                                        displayName: "Country Code",
                                        name: "code",
                                        type: "string",
                                        default: "IN",
                                        description: "Country code (e.g., IN, US)",
                                    },
                                    {
                                        displayName: "Dial Code",
                                        name: "dialCode",
                                        type: "string",
                                        default: "+91",
                                        description: "Dial code with + prefix (e.g., +91, +1)",
                                    },
                                    {
                                        displayName: "Phone Number",
                                        name: "value",
                                        type: "string",
                                        default: "",
                                        description: "Phone number without country code",
                                    },
                                    {
                                        displayName: "Primary",
                                        name: "primary",
                                        type: "boolean",
                                        default: false,
                                        description: "Whether this is the primary phone number",
                                    },
                                    {
                                        displayName: "Type",
                                        name: "type",
                                        type: "options",
                                        options: [
                                            { name: "Mobile", value: "MOBILE" },
                                            { name: "Home", value: "HOME" },
                                            { name: "Work", value: "WORK" },
                                        ],
                                        default: "MOBILE",
                                        description: "Type of phone number",
                                    },
                                ]
                                ,
                            },
                        ],
                    },
                    {
                        displayName: "Requirement",
                        name: "requirementName",
                        type: "string",
                        default: "",
                    },
                    {
                        displayName: "State",
                        name: "state",
                        type: "string",
                        default: "",
                    },
                    {
                        displayName: "Sub Source",
                        name: "subSource",
                        type: "string",
                        default: "",
                    },
                    {
                        displayName: "Twitter",
                        name: "twitter",
                        type: "string",
                        default: "",
                    },
                    {
                        displayName: "UTM Campaign",
                        name: "utmCampaign",
                        type: "string",
                        default: "",
                    },
                    {
                        displayName: "UTM Content",
                        name: "utmContent",
                        type: "string",
                        default: "",
                    },
                    {
                        displayName: "UTM Medium",
                        name: "utmMedium",
                        type: "string",
                        default: "",
                    },
                    {
                        displayName: "UTM Source",
                        name: "utmSource",
                        type: "string",
                        default: "",
                    },
                    {
                        displayName: "UTM Term",
                        name: "utmTerm",
                        type: "string",
                        default: "",
                    },
                    {
                        displayName: "Zipcode",
                        name: "zipcode",
                        type: "string",
                        default: "",
                    },
                ]


            },
            {
                displayName: "Custom Fields",
                name: "customFields",
                description: 'Add custom field',
                type: 'fixedCollection',
                typeOptions: {
                    multipleValues: true,
                },
                default: {},
                displayOptions: {
                    show: {
                        resource: ['lead'],
                        operation: ['create'],
                    }
                },
                options: [
                    {
                        displayName: "Property",
                        name: "property",
                        values: [
                            {
                                displayName: "Field Name or ID",
                                name: "name",
                                type: "options",
                                typeOptions: {
                                    loadOptionsMethod: 'getLeadCustomFields',
                                },
                                default: '',
                                description: 'Choose from the list, or specify an ID using an <a href="https://docs.n8n.io/code/expressions/">expression</a>',
                            },
                            {
                                displayName: "Field Value",
                                name: "value",
                                type: "string",
                                default: '',
                                description: 'Choose from the list, or specify an ID using an <a href="https://docs.n8n.io/code/expressions/">expression</a>',
                            }
                        ]
                    }
                ]
            },
            {

                displayName: 'Lead ID',
                name: 'leadId',
                noDataExpression: true,
                type: 'number',
                default: 530951,
                displayOptions: {
                    show: {
                        operation: ['getLeadById'],
                    }
                }
            },
        ],
    };
    methods = {
        loadOptions: {
            async getLeadCustomFields(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {
                console.log("=== getLeadCustomFields CALLED ===");
                const returnData: INodePropertyOptions[] = [];
                const fields = await getCachedSystemFields.call(this);
                (fields as any[])
                    .filter(field => field.active
                        && field.standard === false
                        && field.type !== 'LOOK_UP'
                        && field.type !== 'MULTI_PICKLIST'
                        && field.type !== 'PICK_LIST'
                    )
                    .forEach(field => {
                        // Add field type information to the display name for PICK_LIST fields
                        returnData.push({
                            name: field.displayName,
                            value: field.name,
                            inputSchema: field.inputSchema,
                        });
                    });
                console.log(`getLeadCustomFields returning ${returnData.length} fields`);
                return returnData;
            },

        }
    };
    /**
     * Prepares the request body for creating a lead
     * @param i - The current item index
     * @returns The prepared body object
     */
    private prepareLeadBody(this: IExecuteFunctions, i: number): IDataObject {
        // Get basic fields
        const firstName = this.getNodeParameter('firstName', i) as string;
        const lastName = this.getNodeParameter('lastName', i) as string;

        // Initialize body with basic fields
        const body: IDataObject = {
            firstName,
            lastName,
        };

        // Handle additional fields
        const additionalFields = this.getNodeParameter('additionalFields', i) as IDataObject;
        console.log("additionalFields->" + JSON.stringify(additionalFields));

        // Iterate through all additional fields and add them to body
        if (additionalFields && Object.keys(additionalFields).length > 0) {
            Object.keys(additionalFields).forEach(fieldName => {
                if (additionalFields[fieldName] !== undefined && additionalFields[fieldName] !== '') {
                    if (fieldName !== 'phoneNumbers' && fieldName !== 'companyPhones' && fieldName !== 'emails') {
                        body[fieldName] = additionalFields[fieldName];
                    }
                }
            });

            // Handle phone numbers
            if (additionalFields.phoneNumbers && (additionalFields.phoneNumbers as IDataObject).phoneNumber) {
                const phoneNumbers = additionalFields.phoneNumbers as IDataObject;
                const phoneNumberArray = phoneNumbers.phoneNumber as IDataObject[];
                body.phoneNumbers = phoneNumberArray.map((phone: IDataObject) => ({
                    type: phone.type,
                    code: phone.code,
                    value: phone.value,
                    dialCode: phone.dialCode,
                    primary: phone.primary,
                }));
            }

            // Handle company phones
            if (additionalFields.companyPhones && (additionalFields.companyPhones as IDataObject).companyPhone) {
                const companyPhones = additionalFields.companyPhones as IDataObject;
                const companyPhoneArray = companyPhones.companyPhone as IDataObject[];
                body.companyPhones = companyPhoneArray.map((phone: IDataObject) => ({
                    type: phone.type,
                    code: phone.code,
                    value: phone.value,
                    dialCode: phone.dialCode,
                    primary: phone.primary,
                }));
            }

            // Handle emails
            if (additionalFields.emails && (additionalFields.emails as IDataObject).email) {
                const emails = additionalFields.emails as IDataObject;
                const emailArray = emails.email as IDataObject[];
                body.emails = emailArray.map((email: IDataObject) => ({
                    type: email.type,
                    value: email.value,
                    primary: email.primary,
                }));
            }
        }

        // Handle custom fields - construct customFieldValues object
        const customFields = this.getNodeParameter('customFields', i) as IDataObject;
        if (customFields && customFields.property) {
            const customFieldArray = customFields.property as IDataObject[];
            const customFieldValues: IDataObject = {};

            customFieldArray.forEach((field: IDataObject) => {
                if (field.name) {
                    // Use picklistValue for picklist fields, value for regular fields
                    const fieldValue = field.picklistValue || field.value;
                    if (fieldValue) {
                        customFieldValues[field.name as string] = fieldValue;
                    }
                }
            });

            if (Object.keys(customFieldValues).length > 0) {
                body.customFieldValues = customFieldValues;
            }
        }

        return body;
    }

    async execute(this: IExecuteFunctions): Promise<INodeExecutionData[][]> {
        const items = this.getInputData();
        const returnData: INodeExecutionData[] = [];
        // const credentials = await this.getCredentials('kylasApi');
        // const apiKey = credentials.apiKey as string;
        const resource = this.getNodeParameter('resource', 0);
        let body: IDataObject;
        let requestMethod: IHttpRequestMethods;
        let endpoint = '';
        for (let i = 0; i < items.length; i++) {
            const operation = this.getNodeParameter('operation', i) as string;
            body = {};
            requestMethod = 'GET';
            endpoint = '';
            if (resource === 'lead') {
                if (operation === 'create') {
                    body = this.prepareLeadBody.call(this, i);
                    requestMethod = 'POST';
                    endpoint = "/v1/leads";
                }
            }
            console.log("body->" + JSON.stringify(body));
            let responseData = await kylasApiRequest.call(
                this,
                requestMethod,
                endpoint,
                body
            );
            console.log("responseData->" + JSON.stringify(responseData));
            const executionData = this.helpers.constructExecutionMetaData(
                this.helpers.returnJsonArray(responseData.data as IDataObject[]),
                { itemData: { item: i } },
            );
            returnData.push(...executionData);

            // if (operation === 'getLeadById') {
            //     const leadId = this.getNodeParameter('leadId', i) as number;
            //     try {
            //         const options: IHttpRequestOptions = {
            //             url: `https://api-qa.sling-dev.com/v1/leads/${leadId}`,
            //             headers: {
            //                 'api-key': `${apiKey}`,
            //             },
            //             json: true,
            //         };
            //         const response = await this.helpers.request(options);
            //         returnData.push({
            //             json: response,
            //             pairedItem: items[i].pairedItem,
            //         });
            //     } catch (error) {
            //         this.logger.error('Failed to fetch APOD data: ' + error.message);
            //         throw new NodeApiError(this.getNode(), error as JsonObject, {
            //             message: 'Failed to fetch APOD data. Please check your API key.',
            //         });
            //         // throw new Error('Failed to fetch APOD data. Please check your API key.');
            //     }
            // } 
        }

        return this.prepareOutputData(returnData);
    }
}

